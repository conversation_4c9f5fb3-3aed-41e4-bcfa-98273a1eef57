import React from 'react'
import { client, blogPostQuery, relatedPostsQuery, BlogPost, urlFor, getCategoryColor } from '@/lib/sanity'
import { notFound } from 'next/navigation'
import Link from 'next/link'
import { format } from 'date-fns'
import { CalendarDays, Clock, ExternalLink, MessageCircle, Users } from 'lucide-react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { BlogImage } from '@/components/blog/BlogImage'
import { PortableTextImage } from '@/components/blog/PortableTextImage'
import { SocialShareButtons } from '@/components/blog/SocialShareButtons'
import { BackButton } from '@/components/blog/BackButton'
import { PortableText } from '@portabletext/react'

interface BlogPostPageProps {
  params: Promise<{
    slug: string
  }>
}

async function getBlogPost(slug: string): Promise<BlogPost | null> {
  try {
    const post = await client.fetch(blogPostQuery, { slug })
    return post
  } catch (error) {
    console.error('Error fetching blog post:', error)
    return null
  }
}

async function getRelatedPosts(postId: string, categoryIds: string[]): Promise<BlogPost[]> {
  try {
    const posts = await client.fetch(relatedPostsQuery, { postId, categoryIds })
    return posts || []
  } catch (error) {
    console.error('Error fetching related posts:', error)
    return []
  }
}

// Portable Text components for rich text rendering
const portableTextComponents = {
  types: {
    image: ({ value }: any) => <PortableTextImage value={value} />,
    codeBlock: ({ value }: any) => (
      <div className="my-8">
        <div className="bg-muted rounded-lg p-4 overflow-x-auto">
          <div className="flex items-center justify-between mb-2">
            <span className="text-xs text-muted-foreground uppercase tracking-wide">
              {value.language || 'Code'}
            </span>
          </div>
          <pre className="text-sm">
            <code className="text-foreground">{value.code}</code>
          </pre>
        </div>
      </div>
    ),
  },
  marks: {
    link: ({ children, value }: any) => (
      <a
        href={value.href}
        className="text-primary hover:text-primary/80 underline underline-offset-2 decoration-2"
        target={value.blank ? '_blank' : undefined}
        rel={value.blank ? 'noopener noreferrer' : undefined}
      >
        {children}
        {value.blank && <ExternalLink className="inline h-3 w-3 ml-1" />}
      </a>
    ),
    code: ({ children }: any) => (
      <code className="bg-muted px-2 py-1 rounded text-sm font-mono text-foreground">
        {children}
      </code>
    ),
  },
  block: {
    h1: ({ children }: any) => (
      <h1 className="text-4xl font-bold text-foreground mt-12 mb-6 leading-tight">{children}</h1>
    ),
    h2: ({ children }: any) => (
      <h2 className="text-3xl font-semibold text-foreground mt-10 mb-5 leading-tight">{children}</h2>
    ),
    h3: ({ children }: any) => (
      <h3 className="text-2xl font-semibold text-foreground mt-8 mb-4 leading-tight">{children}</h3>
    ),
    h4: ({ children }: any) => (
      <h4 className="text-xl font-semibold text-foreground mt-6 mb-3 leading-tight">{children}</h4>
    ),
    normal: ({ children }: any) => (
      <p className="text-foreground leading-relaxed mb-6 text-lg">{children}</p>
    ),
    blockquote: ({ children }: any) => (
      <blockquote className="border-l-4 border-primary pl-6 py-2 italic text-muted-foreground my-8 bg-muted/30 rounded-r-lg">
        <div className="text-lg">{children}</div>
      </blockquote>
    ),
  },
  list: {
    bullet: ({ children }: any) => (
      <ul className="list-disc list-outside ml-6 space-y-2 mb-6 text-foreground text-lg">{children}</ul>
    ),
    number: ({ children }: any) => (
      <ol className="list-decimal list-outside ml-6 space-y-2 mb-6 text-foreground text-lg">{children}</ol>
    ),
  },
}

export default async function BlogPostPage({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    notFound()
  }

  const categoryIds = post.categories?.map(cat => cat._id) || []
  const relatedPosts = await getRelatedPosts(post._id, categoryIds)

  return (
    <>
      {/* JSON-LD Structured Data for Article */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'Article',
            headline: post.title,
            description: post.excerpt || post.title,
            image: post.mainImage
              ? [urlFor(post.mainImage).width(1200).height(630).url()]
              : undefined,
            datePublished: post.publishedAt,
            dateModified: post.updatedAt || post.publishedAt,
            author: post.author
              ? {
                  '@type': 'Person',
                  name: post.author.name,
                  url: post.author.socialLinks?.website,
                  sameAs: [
                    post.author.socialLinks?.twitter,
                    post.author.socialLinks?.linkedin,
                  ].filter(Boolean),
                }
              : undefined,
            publisher: {
              '@type': 'Organization',
              name: 'AdMesh',
              url: 'https://useadmesh.com',
              logo: {
                '@type': 'ImageObject',
                url: 'https://useadmesh.com/logo.svg',
                width: 200,
                height: 200,
              },
            },
            mainEntityOfPage: {
              '@type': 'WebPage',
              '@id': `https://useadmesh.com/blog/${post.slug.current}`,
            },
            articleSection: post.categories?.map(cat => cat.title) || undefined,
            keywords: post.seo?.keywords?.join(', ') || undefined,
            wordCount: post.body ? JSON.stringify(post.body).length / 5 : undefined,
            timeRequired: post.readingTime || post.estimatedReadingTime
              ? `PT${post.readingTime || post.estimatedReadingTime}M`
              : undefined,
            inLanguage: 'en-US',
          }),
        }}
      />

      {/* Breadcrumb Structured Data */}
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify({
            '@context': 'https://schema.org',
            '@type': 'BreadcrumbList',
            itemListElement: [
              {
                '@type': 'ListItem',
                position: 1,
                name: 'Home',
                item: 'https://useadmesh.com',
              },
              {
                '@type': 'ListItem',
                position: 2,
                name: 'Blog',
                item: 'https://useadmesh.com/blog',
              },
              {
                '@type': 'ListItem',
                position: 3,
                name: post.title,
                item: `https://useadmesh.com/blog/${post.slug.current}`,
              },
            ],
          }),
        }}
      />

      <div className="min-h-screen bg-background">
        <div className="container mx-auto px-4 py-12 max-w-5xl">
          {/* Back Button - Medium Style */}
          <div className="mb-12">
            <BackButton />
          </div>

          {/* Article Header - Medium Layout */}
          <article className="space-y-16">
            <header className="max-w-4xl mx-auto">
              {/* Title - Medium Typography */}
              <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold text-foreground mb-4 leading-[1.1] tracking-tight">
                {post.title}
              </h1>

              {/* Subtitle - Medium Style */}
              {post.excerpt && (
                <h2 className="text-xl md:text-2xl text-muted-foreground mb-8 leading-relaxed font-normal">
                  {post.excerpt}
                </h2>
              )}

              {/* Author and Meta Information - Medium Layout */}
              {post.author && (
                <div className="flex items-center gap-4 py-6 border-b border-border">
                  <Avatar className="h-12 w-12 flex-shrink-0">
                    <AvatarImage
                      src={post.author.image ? urlFor(post.author.image).width(96).height(96).url() : undefined}
                      alt={post.author.name}
                    />
                    <AvatarFallback className="bg-muted text-foreground font-medium">
                      {post.author.name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center gap-3 mb-1 flex-wrap">
                      <span className="font-medium text-foreground text-base">{post.author.name}</span>
                      {/* <Button
                        variant="outline"
                        size="sm"
                        className="h-7 px-3 text-xs font-medium border-foreground text-foreground hover:bg-foreground hover:text-background"
                      >
                        Follow
                      </Button> */}
                    </div>

                    <div className="flex items-center gap-2 text-sm text-muted-foreground flex-wrap">
                      <span>{format(new Date(post.publishedAt), 'MMM d, yyyy')}</span>
                      {(post.readingTime || post.estimatedReadingTime) && (
                        <>
                          <span>·</span>
                          <span>{post.readingTime || post.estimatedReadingTime} min read</span>
                        </>
                      )}
                    </div>
                  </div>
                </div>
              )}
            </header>

            {/* Featured Image - Commented out for cleaner UI */}
            {/*
            {post.mainImage && (
              <div className="relative h-64 md:h-96 lg:h-[500px] w-full overflow-hidden rounded-2xl shadow-2xl bg-muted max-w-5xl mx-auto">
                <BlogImage
                  src={urlFor(post.mainImage).width(1400).height(800).url()}
                  alt={post.mainImage.alt || post.title}
                  fill
                  className="object-cover"
                  sizes="(max-width: 768px) 100vw, (max-width: 1200px) 80vw, 1200px"
                  priority
                />
              </div>
            )}
            */}

            {/* Article Content - Medium Typography */}
            {post.body && (
              <div className="max-w-[700px] mx-auto">
                <div className="prose prose-lg max-w-none
                  prose-headings:text-foreground prose-headings:font-bold prose-headings:tracking-tight
                  prose-h1:text-3xl prose-h1:leading-tight prose-h1:mb-6 prose-h1:mt-12
                  prose-h2:text-2xl prose-h2:leading-tight prose-h2:mb-4 prose-h2:mt-10
                  prose-h3:text-xl prose-h3:leading-tight prose-h3:mb-3 prose-h3:mt-8
                  prose-p:text-foreground prose-p:text-xl prose-p:leading-relaxed prose-p:mb-6
                  prose-strong:text-foreground prose-strong:font-semibold
                  prose-a:text-foreground prose-a:underline prose-a:decoration-2 prose-a:underline-offset-2 hover:prose-a:decoration-primary
                  prose-blockquote:border-l-4 prose-blockquote:border-border prose-blockquote:pl-6 prose-blockquote:italic prose-blockquote:text-muted-foreground prose-blockquote:text-xl prose-blockquote:leading-relaxed
                  prose-code:text-foreground prose-code:bg-muted prose-code:px-2 prose-code:py-1 prose-code:rounded prose-code:text-base
                  prose-pre:bg-muted prose-pre:border prose-pre:border-border
                  prose-ul:text-xl prose-ul:leading-relaxed prose-ul:mb-6
                  prose-ol:text-xl prose-ol:leading-relaxed prose-ol:mb-6
                  prose-li:text-foreground prose-li:mb-2
                  prose-img:rounded-lg prose-img:shadow-lg">
                  <PortableText
                    value={post.body}
                    components={portableTextComponents}
                  />
                </div>
              </div>
            )}

            {/* Bottom Section - Medium Style */}
            <div className="max-w-[700px] mx-auto space-y-12 pt-12 mt-16">

              {/* Categories - Medium Style at Bottom */}
              {post.categories && post.categories.length > 0 && (
                <div className="flex flex-wrap gap-3">
                  {post.categories.map((category) => (
                    <Link
                      key={category._id}
                      href={`/blog?category=${category.slug.current}`}
                      className="text-sm text-muted-foreground hover:text-foreground transition-colors bg-muted px-3 py-1 rounded-full"
                    >
                      {category.title}
                    </Link>
                  ))}
                </div>
              )}

              {/* Social Share - Medium Style */}
              <div className="flex items-center justify-center py-6 border-y border-border">
                <SocialShareButtons
                  title={post.title}
                  url={`https://useadmesh.com/blog/${post.slug.current}`}
                />
              </div>

              {/* Author Information - Medium Style */}
              {post.author && (
                <div className="flex items-start gap-4 py-8">
                  {post.author.image && (
                    <Avatar className="h-16 w-16 flex-shrink-0">
                      <AvatarImage
                        src={urlFor(post.author.image).width(64).height(64).url()}
                        alt={post.author.name}
                      />
                      <AvatarFallback className="text-lg font-semibold">
                        {post.author.name.charAt(0)}
                      </AvatarFallback>
                    </Avatar>
                  )}
                  <div className="flex-1 min-w-0">
                    <div className="mb-3">
                      <h4 className="text-lg font-semibold text-foreground mb-1">{post.author.name}</h4>
                      {post.author.bio && (
                        <div className="text-base text-muted-foreground leading-relaxed">
                          <PortableText
                            value={post.author.bio}
                            components={{
                              block: {
                                normal: ({ children }: any) => <p className="mb-2">{children}</p>,
                              },
                            }}
                          />
                        </div>
                      )}
                    </div>
                    {post.author.socialLinks && (
                      <div className="flex gap-3">
                        {post.author.socialLinks.twitter && (
                          <Button variant="ghost" size="sm" asChild className="h-8 px-3 text-sm">
                            <a href={post.author.socialLinks.twitter} target="_blank" rel="noopener noreferrer">
                              Twitter
                            </a>
                          </Button>
                        )}
                        {post.author.socialLinks.linkedin && (
                          <Button variant="ghost" size="sm" asChild className="h-8 px-3 text-sm">
                            <a href={post.author.socialLinks.linkedin} target="_blank" rel="noopener noreferrer">
                              LinkedIn
                            </a>
                          </Button>
                        )}
                        {post.author.socialLinks.website && (
                          <Button variant="ghost" size="sm" asChild className="h-8 px-3 text-sm">
                            <a href={post.author.socialLinks.website} target="_blank" rel="noopener noreferrer">
                              Website
                            </a>
                          </Button>
                        )}
                      </div>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Related Posts - Medium Style */}
            {relatedPosts.length > 0 && (
              <div className="border-t border-border pt-16 mt-20">
                <div className="max-w-6xl mx-auto">
                  <div className="mb-12">
                    <h3 className="text-2xl font-bold text-foreground mb-2">More from AdMesh</h3>
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
                    {relatedPosts.map((relatedPost) => (
                      <article key={relatedPost._id} className="group">
                        <Link href={`/blog/${relatedPost.slug.current}`} className="block">
                          {relatedPost.mainImage && (
                            <div className="relative aspect-[16/10] w-full overflow-hidden rounded-lg bg-muted mb-4">
                              <BlogImage
                                src={urlFor(relatedPost.mainImage).width(400).height(250).url()}
                                alt={relatedPost.mainImage.alt || relatedPost.title}
                                fill
                                className="object-cover group-hover:scale-105 transition-transform duration-300"
                                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                              />
                            </div>
                          )}
                          <div className="space-y-3">
                            <h4 className="text-lg font-semibold text-foreground group-hover:text-primary transition-colors line-clamp-2 leading-tight">
                              {relatedPost.title}
                            </h4>
                            {relatedPost.excerpt && (
                              <p className="text-muted-foreground line-clamp-3 leading-relaxed text-base">
                                {relatedPost.excerpt}
                              </p>
                            )}
                            <div className="flex items-center gap-2 text-sm text-muted-foreground">
                              {relatedPost.author && (
                                <>
                                  <span className="font-medium">{relatedPost.author.name}</span>
                                  <span>·</span>
                                </>
                              )}
                              <span>{format(new Date(relatedPost.publishedAt), 'MMM dd')}</span>
                            </div>
                          </div>
                        </Link>
                      </article>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </article>
        </div>
      </div>
    </>
  )
}

// Generate metadata for SEO
export async function generateMetadata({ params }: BlogPostPageProps) {
  const { slug } = await params
  const post = await getBlogPost(slug)

  if (!post) {
    return {
      title: 'Post Not Found | AdMesh Blog',
      description: 'The requested blog post could not be found.',
    }
  }

  const title = post.seo?.metaTitle || post.title
  const description = post.seo?.metaDescription || post.excerpt || `Read ${post.title} on the AdMesh blog`
  const keywords = post.seo?.keywords || []
  const noIndex = post.seo?.noIndex || false

  return {
    title: `${title} | AdMesh Blog`,
    description,
    keywords: keywords.length > 0 ? keywords.join(', ') : undefined,
    authors: post.author ? [{ name: post.author.name }] : undefined,
    robots: noIndex ? 'noindex, nofollow' : 'index, follow',
    openGraph: {
      title,
      description,
      type: 'article',
      publishedTime: post.publishedAt,
      modifiedTime: post.updatedAt,
      authors: post.author ? [post.author.name] : undefined,
      tags: post.categories?.map(cat => cat.title) || undefined,
      images: post.mainImage
        ? [
            {
              url: urlFor(post.mainImage).width(1200).height(630).url(),
              width: 1200,
              height: 630,
              alt: post.mainImage.alt || post.title,
            },
          ]
        : undefined,
      siteName: 'AdMesh',
    },
    twitter: {
      card: 'summary_large_image',
      title,
      description,
      images: post.mainImage
        ? [urlFor(post.mainImage).width(1200).height(630).url()]
        : undefined,
      creator: post.author?.socialLinks?.twitter ? `@${post.author.socialLinks.twitter.split('/').pop()}` : undefined,
    },
    alternates: {
      canonical: `/blog/${post.slug.current}`,
    },
  }
}
